import { But<PERSON>, <PERSON>, Switch, Row, Col } from 'antd'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useQueryClient } from '@tanstack/react-query'
import { useForm } from 'antd/es/form/Form'
import useDeepCompareEffect from 'use-deep-compare-effect'
import { useMedicalRecord, MEDICAL_RECORD_KEYS } from '../../queryHooks/useMedicalRecord'
import { displayDate, handleError } from '../../common/helpers'
import { updateListItemService } from '../../common/services'
import lists from '../../common/lists'
import { FORM_MODE } from '../../common/constant'
import MedicalRecordField, { MEDICAL_RECORD_FIELD_TYPE } from './MedicalRecordField'
import useApp from 'antd/es/app/useApp'
import DocumentStore from '../../common/components/DocumentStore/DocumentStore'
import PdfViewer from '../../common/components/PdfViewer'
import dayjs from '../../common/dayjs'
import { usePatientVisit } from '../Visit/hooks/usePatientVisit'
import fvLogoWithText from '../../assets/logoFV-withtext.png'
import { MODULE_AUTH } from '../../store/auth'
import { handlePrintPDF } from '../../SI/helper'
import { approvalLetterContent } from './MedicalRecordConstant'
import AsyncButton from '../../common/components/AsyncButton'
import { usePatient } from '../../queryHooks/usePatient'
import PopulatePDF from '../../common/components/PopulatePDF'
import config from '../../common/config'

const ApprovalLetterForm = ({ mainVisit, selectedMedicalRecordId, patientId }) => {
  const onlyUsePopulatePDF = true // disable raw html form

  const [form] = useForm()
  const app = useApp()
  const queryClient = useQueryClient()
  const { currentUser } = useSelector((state) => state[MODULE_AUTH])

  // PDF Template Configuration
  const pdfTemplates = [
    {
      id: 'approval_letter_vn',
      name: 'Giấy chấp thuận - Tiếng Việt',
      url:
        config.HOME_PAGE +
        'template/073 CNS - Consent to Social Insurance Coverage Limitations at FV Hospital - VN.pdf',
    },
    {
      id: 'approval_letter_en',
      name: 'Approval Letter - English',
      url:
        config.HOME_PAGE +
        'template/073 CNS - Consent to Social Insurance Coverage Limitations at FV Hospital - Eng.pdf',
    },
  ]

  // hooks
  const { medicalRecordDetail, medicalCondition } = useMedicalRecord({
    medicalRecordFormId: selectedMedicalRecordId,
  })
  const {
    data: { healthInsuranceCards },
  } = usePatientVisit(mainVisit?.patient_visit_id)
  const firstCard = healthInsuranceCards?.[0]
  const { patient } = usePatient({ patientId: patientId || mainVisit?.patient_id })

  const [formMode, setFormMode] = useState(FORM_MODE.view)
  const [openDocumentStore, setOpenDocumentStore] = useState(false)
  const [attachments, setAttachments] = useState([])
  const [viewFileOnly, setViewFileOnly] = useState()

  const isEdit = formMode === FORM_MODE.edit

  const fullName = patient?.Fullname || ''
  const sex = patient?.Sex === 'Male' ? 'Nam' : 'Nữ'
  const nationId = patient?.Nation_id || ''
  const dob = displayDate(patient?.DOB, 'DD/MM/YYYY')
  const HN = patient?.HN || ''

  // Default values for PDF form fields
  const defaultPdfValues = {
    fullName: fullName,
    sex: sex,
    dob: dob,
    hn: HN,
    address: firstCard?.card_address || '',
    cccd: nationId,
    phone: patient?.Phone || '',
    card: firstCard?.card_code || '',
    cardDate: firstCard?.effective_date
      ? dayjs(firstCard?.effective_date).format('DD/MM/YYYY')
      : '',
  }

  // Parse saved field values from note column
  const savedFieldValues = (() => {
    try {
      return medicalRecordDetail?.note ? JSON.parse(medicalRecordDetail.note) : {}
    } catch {
      return {}
    }
  })()

  // Handle PDF save success
  const handlePdfSaveSuccess = async ({ fieldValues, templateId, fileName }) => {
    try {
      // Save field values to note column as JSON
      const noteData = {
        pdfFieldValues: fieldValues,
        templateId: templateId,
        lastSavedFileName: fileName,
        lastSavedAt: new Date().toISOString(),
      }

      await updateListItemService(lists.medical_record_form, selectedMedicalRecordId, {
        note: JSON.stringify(noteData),
        lu_user_id: currentUser?.User_id,
      })

      // Invalidate the query cache to force a refresh
      queryClient.invalidateQueries({
        queryKey: [MEDICAL_RECORD_KEYS.MEDICAL_RECORD, selectedMedicalRecordId],
      })

      app.message.success('Lưu PDF và thông tin thành công')
    } catch (error) {
      handleError(error, 'handlePdfSaveSuccess')
      app.message.error('Không thể lưu thông tin PDF')
    }
  }

  // default values
  useDeepCompareEffect(() => {
    form.setFieldsValue({
      signedDateTime: medicalRecordDetail?.signed_date_time
        ? dayjs(medicalRecordDetail.signed_date_time)
        : dayjs(),
    })
  }, [medicalRecordDetail, medicalCondition, form])

  // auto show attachment
  useEffect(() => {
    setViewFileOnly(!!attachments[0] && !isEdit)
  }, [attachments, isEdit])

  const handleSaveMedicalRecordForm = async () => {
    if (!selectedMedicalRecordId) {
      return
    }

    const values = form.getFieldsValue()

    try {
      const newRecord = {
        lu_user_id: currentUser?.User_id,
        signed_date_time: values.signedDateTime,
      }

      await updateListItemService(lists.medical_record_form, selectedMedicalRecordId, newRecord)

      // Invalidate the query cache to force a refresh of the medical record data
      queryClient.invalidateQueries({
        queryKey: [MEDICAL_RECORD_KEYS.MEDICAL_RECORD, selectedMedicalRecordId],
      })

      app.message.success('Lưu thành công')
    } catch (error) {
      handleError(error, 'handleSaveMedicalRecordForm')
    }
  }

  return (
    <div>
      <div
        className="sticky-top d-flex justify-content-between align-items-center gap-2"
        style={{ top: 105 }}>
        <div></div>
        <div className="d-flex align-items-center gap-2">
          {/* toggle to view file or form */}
          <div className="d-flex align-items-center me-2 gap-2">
            <Switch onChange={() => setViewFileOnly(!viewFileOnly)} checked={viewFileOnly} /> Chỉ
            xem File đính kèm
          </div>
          <Button
            hidden={onlyUsePopulatePDF}
            variant={isEdit ? 'outlined' : 'solid'}
            color={'blue'}
            icon={<i className="fa fa-edit" />}
            onClick={() => setFormMode(isEdit ? FORM_MODE.view : FORM_MODE.edit)}>
            {isEdit ? 'Tắt Chỉnh sửa' : 'Chỉnh sửa'}
          </Button>
          <AsyncButton
            icon={<i className="fa fa-save" />}
            hidden={!isEdit || onlyUsePopulatePDF}
            onClick={handleSaveMedicalRecordForm}>
            Lưu
          </AsyncButton>
          <Button
            variant="solid"
            color="green"
            icon={
              openDocumentStore ? <i className="fa fa-close" /> : <i className="fa fa-upload" />
            }
            onClick={() => setOpenDocumentStore(!openDocumentStore)}>
            {openDocumentStore ? 'Đóng' : 'Mở'} upload
          </Button>

          <Button
            hidden={onlyUsePopulatePDF}
            icon={<i className="fa fa-print" />}
            variant="solid"
            color="cyan"
            onClick={() => {
              setViewFileOnly(false)
              setOpenDocumentStore(false)
              setFormMode(FORM_MODE.view)

              handlePrintPDF(`GiayChapThuan_${medicalRecordDetail?.title || ''}`)
            }}>
            In phiếu
          </Button>
        </div>
      </div>

      <div
        className="mt-2 mb-4 shadow-md p-3 rounded-sm"
        style={{ display: openDocumentStore ? 'block' : 'none' }}>
        <DocumentStore
          dataSource={lists.medical_record_form.listName}
          parentID={4} // 4 is DocumentStore
          storeID={selectedMedicalRecordId}
          mode={'Edit'}
          setAttachments={setAttachments}
        />
      </div>

      <div
        id="medical-record-form-print"
        hidden={viewFileOnly || onlyUsePopulatePDF}
        className="mt-3 px-4">
        <Form form={form} layout="vertical">
          <div className="mb-4">
            <div>
              <img
                src={fvLogoWithText}
                alt="FV Hospital"
                style={{ height: '40px', marginBottom: '10px' }}
                onError={(e) => {
                  e.target.style.display = 'none'
                  e.target.nextSibling.style.display = 'block'
                }}
              />
              <div style={{ display: 'none' }}>FV THOMSON</div>
            </div>
            <h4 className="text-center fw-bold">
              GIẤY CHẤP THUẬN GIỚI HẠN CHI TRẢ CỦA BẢO HIỂM XÃ HỘI TẠI BỆNH VIỆN FV
            </h4>
          </div>

          {/* Patient Information */}
          <div className="fw-bold text-primary my-2">Thông tin cá nhân</div>
          <Row gutter={24}>
            <Col span={14}>
              <div>Họ tên: {fullName}</div>
              <div>Giới tính: {sex}</div>
              <div>Số điện thoại: </div>
              <div>Địa chỉ: {firstCard?.card_address || ''}</div>
              <div>Số CCCD: {nationId}</div>
              <div>Số thẻ bảo hiểm xã hội: {firstCard?.card_code || ''}</div>
            </Col>

            <Col span={10}>
              <div>Ngày sinh: {dob}</div>
              <div>Mã số bệnh nhân (HN): {HN}</div>
            </Col>
          </Row>

          <div className="fw-bold text-primary my-2 mt-3">
            Thông tin cá nhân của người ký thay bệnh nhân
          </div>
          <Row gutter={24}>
            <Col span={14}>
              <div>Họ tên:</div>
              <div>Số CCCD:</div>
              <div>Địa chỉ:</div>
            </Col>
            <Col span={10}>
              <div>Mối quan hệ:</div>
              <div>Số điện thoại:</div>
            </Col>
          </Row>

          <div className="mt-3">
            <div dangerouslySetInnerHTML={{ __html: approvalLetterContent }}></div>

            <div className="d-flex gap-2 align-items-center mt-5">
              <MedicalRecordField
                form={form}
                formMode={formMode}
                label="Ngày ký:"
                labelBold={false}
                labelClassName="w-[70px]"
                fieldName="signedDateTime"
                fieldType={MEDICAL_RECORD_FIELD_TYPE.DATE}
              />
            </div>
          </div>
        </Form>
      </div>

      {/* Dynamic PDF Form Filling */}
      {pdfTemplates.length > 0 && !viewFileOnly && (
        <div className="mt-3">
          <PopulatePDF
            templates={pdfTemplates}
            storeID={selectedMedicalRecordId}
            dataSource={lists.medical_record_form.listName}
            onSaveSuccess={handlePdfSaveSuccess}
            defaultValues={defaultPdfValues}
            savedFieldValues={savedFieldValues?.pdfFieldValues || {}}
          />
        </div>
      )}

      <div hidden={!viewFileOnly} className="mt-3">
        <PdfViewer
          serverRelativeUrl={attachments[0]?.ServerRelativeUrl}
          fileName={attachments[0]?.Name}
        />
      </div>
    </div>
  )
}

export default ApprovalLetterForm
